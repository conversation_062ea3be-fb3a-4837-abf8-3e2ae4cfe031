<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quiz - CNXH Việt Nam</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .quiz-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            padding: 2rem 0;
        }
        
        .quiz-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .quiz-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .quiz-header h1 {
            color: #dc2626;
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .quiz-progress {
            background: #f1f5f9;
            height: 10px;
            border-radius: 5px;
            margin: 2rem 0;
            overflow: hidden;
        }
        
        .quiz-progress-bar {
            background: linear-gradient(135deg, #dc2626, #fbbf24);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .question-container {
            margin-bottom: 2rem;
        }
        
        .question-number {
            color: #6b7280;
            font-size: 1rem;
            margin-bottom: 1rem;
        }
        
        .question-text {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2rem;
            line-height: 1.4;
        }
        
        .options-container {
            display: grid;
            gap: 1rem;
        }
        
        .option-btn {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            padding: 1.5rem;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 1.1rem;
            position: relative;
        }
        
        .option-btn:hover {
            background: #e5e7eb;
            border-color: #dc2626;
            transform: translateY(-2px);
        }
        
        .option-btn.selected {
            background: #dbeafe;
            border-color: #3b82f6;
        }
        
        .option-btn.correct {
            background: #d1fae5;
            border-color: #10b981;
        }
        
        .option-btn.incorrect {
            background: #fee2e2;
            border-color: #ef4444;
        }
        
        .option-btn.disabled {
            cursor: not-allowed;
            opacity: 0.7;
        }
        
        .explanation {
            background: #fef3c7;
            border-left: 4px solid #fbbf24;
            padding: 1.5rem;
            border-radius: 0 10px 10px 0;
            margin: 2rem 0;
            display: none;
        }
        
        .explanation.show {
            display: block;
            animation: slideDown 0.3s ease;
        }
        
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .quiz-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 3rem;
        }
        
        .control-btn {
            background: linear-gradient(135deg, #dc2626, #991b1b);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
        }
        
        .control-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .back-btn {
            background: #6b7280;
        }
        
        .back-btn:hover {
            background: #4b5563;
            box-shadow: 0 8px 25px rgba(107, 114, 128, 0.3);
        }
        
        .quiz-result {
            text-align: center;
            display: none;
        }
        
        .result-score {
            font-size: 4rem;
            font-weight: 700;
            color: #dc2626;
            margin: 2rem 0;
        }
        
        .result-message {
            font-size: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .result-details {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .home-link {
            display: inline-block;
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
            text-decoration: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 1rem;
        }
        
        .home-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }
    </style>
</head>
<body>
    <div class="quiz-page">
        <div class="quiz-container">
            <div class="quiz-header">
                <h1><i class="fas fa-question-circle"></i> Quiz CNXH Việt Nam</h1>
                <p>Kiểm tra kiến thức của bạn về Chủ nghĩa Xã hội Việt Nam</p>
                <div class="quiz-progress">
                    <div class="quiz-progress-bar" id="progressBar"></div>
                </div>
            </div>

            <div id="quizContent">
                <div class="question-container">
                    <div class="question-number" id="questionNumber">Câu hỏi 1/5</div>
                    <div class="question-text" id="questionText">Đang tải câu hỏi...</div>
                    
                    <div class="options-container" id="optionsContainer">
                        <!-- Options will be loaded here -->
                    </div>
                    
                    <div class="explanation" id="explanation">
                        <strong>Giải thích:</strong>
                        <p id="explanationText"></p>
                    </div>
                </div>
                
                <div class="quiz-controls">
                    <a href="index.html" class="control-btn back-btn">
                        <i class="fas fa-arrow-left"></i> Về trang chủ
                    </a>
                    <button class="control-btn" id="nextBtn" onclick="nextQuestion()" disabled>
                        Câu tiếp theo <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>

            <div id="quizResult" class="quiz-result">
                <h2>Kết quả Quiz</h2>
                <div class="result-score" id="finalScore">0/5</div>
                <div class="result-message" id="resultMessage"></div>
                <div class="result-details">
                    <p><strong>Thời gian hoàn thành:</strong> <span id="completionTime"></span></p>
                    <p><strong>Độ chính xác:</strong> <span id="accuracy"></span></p>
                </div>
                <a href="index.html" class="home-link">
                    <i class="fas fa-home"></i> Về trang chủ
                </a>
                <button class="control-btn" onclick="restartQuiz()">
                    <i class="fas fa-redo"></i> Làm lại
                </button>
            </div>
        </div>
    </div>

    <script>
        const quizQuestions = [
            {
                question: "Việt Nam có bao nhiêu đặc trưng cơ bản của Chủ nghĩa Xã hội?",
                options: ["6 đặc trưng", "8 đặc trưng", "10 đặc trưng", "12 đặc trưng"],
                correct: 1,
                explanation: "Theo Cương lĩnh xây dựng đất nước trong thời kỳ quá độ lên CNXH (bổ sung 2011), Việt Nam có 8 đặc trưng cơ bản của CNXH."
            },
            {
                question: "Tại sao Việt Nam chọn con đường bỏ qua chế độ tư bản chủ nghĩa?",
                options: [
                    "Do điều kiện kinh tế không cho phép",
                    "Theo con đường cách mạng vô sản của Hồ Chí Minh",
                    "Do áp lực từ các nước xã hội chủ nghĩa",
                    "Do không có tư bản tư nhân"
                ],
                correct: 1,
                explanation: "Chủ tịch Hồ Chí Minh đã xác định con đường cách mạng vô sản cho Việt Nam, đi thẳng lên chế độ xã hội chủ nghĩa sau khi hoàn thành cách mạng."
            },
            {
                question: "Năm nào Việt Nam chính thức bước vào thời kỳ quá độ lên CNXH?",
                options: ["1945", "1954", "1960", "1975"],
                correct: 2,
                explanation: "Năm 1960, sau 9 năm kháng chiến chống Pháp và thời gian cải tạo xã hội, Việt Nam chính thức bước vào thời kỳ quá độ lên CNXH."
            },
            {
                question: "Đặc trưng nào KHÔNG thuộc 8 đặc trưng cơ bản của CNXH Việt Nam?",
                options: [
                    "Dân giàu, nước mạnh, dân chủ, công bằng, văn minh",
                    "Do nhân dân làm chủ",
                    "Có chế độ đa đảng cạnh tranh",
                    "Có nền văn hóa tiên tiến, đậm đà bản sắc dân tộc"
                ],
                correct: 2,
                explanation: "Chế độ đa đảng cạnh tranh không phải là đặc trưng của CNXH Việt Nam. Việt Nam có Nhà nước pháp quyền XHCN do Đảng Cộng sản lãnh đạo."
            },
            {
                question: "Phương hướng nào là nhiệm vụ trung tâm trong xây dựng CNXH ở Việt Nam?",
                options: [
                    "Xây dựng Đảng trong sạch, vững mạnh",
                    "Đẩy mạnh công nghiệp hóa, hiện đại hóa",
                    "Xây dựng nền dân chủ xã hội chủ nghĩa",
                    "Thực hiện đường lối đối ngoại độc lập"
                ],
                correct: 1,
                explanation: "Đẩy mạnh công nghiệp hóa, hiện đại hóa gắn với phát triển kinh tế tri thức là nhiệm vụ trung tâm, nhằm phát triển nhanh và bền vững đất nước."
            }
        ];

        let currentQuestion = 0;
        let userAnswers = [];
        let score = 0;
        let startTime = Date.now();
        let selectedAnswer = null;

        function loadQuestion() {
            const question = quizQuestions[currentQuestion];
            document.getElementById('questionNumber').textContent = `Câu hỏi ${currentQuestion + 1}/${quizQuestions.length}`;
            document.getElementById('questionText').textContent = question.question;
            
            const optionsContainer = document.getElementById('optionsContainer');
            optionsContainer.innerHTML = '';
            
            question.options.forEach((option, index) => {
                const optionBtn = document.createElement('button');
                optionBtn.className = 'option-btn';
                optionBtn.textContent = `${String.fromCharCode(65 + index)}. ${option}`;
                optionBtn.onclick = () => selectAnswer(index);
                optionsContainer.appendChild(optionBtn);
            });
            
            updateProgress();
            document.getElementById('explanation').classList.remove('show');
            document.getElementById('nextBtn').disabled = true;
            selectedAnswer = null;
        }

        function selectAnswer(answerIndex) {
            if (selectedAnswer !== null) return;
            
            selectedAnswer = answerIndex;
            const question = quizQuestions[currentQuestion];
            const options = document.querySelectorAll('.option-btn');
            
            options.forEach((option, index) => {
                option.classList.add('disabled');
                if (index === question.correct) {
                    option.classList.add('correct');
                } else if (index === answerIndex && index !== question.correct) {
                    option.classList.add('incorrect');
                }
            });
            
            userAnswers[currentQuestion] = answerIndex;
            if (answerIndex === question.correct) {
                score++;
            }
            
            document.getElementById('explanationText').textContent = question.explanation;
            document.getElementById('explanation').classList.add('show');
            document.getElementById('nextBtn').disabled = false;
        }

        function nextQuestion() {
            currentQuestion++;
            if (currentQuestion < quizQuestions.length) {
                loadQuestion();
            } else {
                showResult();
            }
        }

        function updateProgress() {
            const progress = ((currentQuestion + 1) / quizQuestions.length) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
        }

        function showResult() {
            const endTime = Date.now();
            const completionTime = Math.round((endTime - startTime) / 1000);
            const accuracy = Math.round((score / quizQuestions.length) * 100);
            
            document.getElementById('quizContent').style.display = 'none';
            document.getElementById('quizResult').style.display = 'block';
            
            document.getElementById('finalScore').textContent = `${score}/${quizQuestions.length}`;
            document.getElementById('completionTime').textContent = `${completionTime} giây`;
            document.getElementById('accuracy').textContent = `${accuracy}%`;
            
            let message = '';
            if (accuracy >= 80) {
                message = '🎉 Xuất sắc! Bạn đã nắm vững kiến thức về CNXH Việt Nam.';
            } else if (accuracy >= 60) {
                message = '👍 Khá tốt! Bạn có hiểu biết cơ bản về CNXH Việt Nam.';
            } else {
                message = '📚 Hãy ôn tập thêm để hiểu rõ hơn về CNXH Việt Nam.';
            }
            
            document.getElementById('resultMessage').textContent = message;
        }

        function restartQuiz() {
            currentQuestion = 0;
            userAnswers = [];
            score = 0;
            startTime = Date.now();
            selectedAnswer = null;
            
            document.getElementById('quizContent').style.display = 'block';
            document.getElementById('quizResult').style.display = 'none';
            
            loadQuestion();
        }

        // Initialize quiz
        document.addEventListener('DOMContentLoaded', () => {
            loadQuestion();
        });
    </script>
</body>
</html>
