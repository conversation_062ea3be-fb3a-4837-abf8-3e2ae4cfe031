<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Timeline - CNXH Việt Nam</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .timeline-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            padding: 2rem 0;
        }
        
        .timeline-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .timeline-header {
            text-align: center;
            color: white;
            margin-bottom: 4rem;
        }
        
        .timeline-header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .timeline-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .timeline-wrapper {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .timeline-line {
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #dc2626, #fbbf24);
            transform: translateX(-50%);
            border-radius: 2px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 4rem;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }
        
        .timeline-item.animate {
            opacity: 1;
            transform: translateY(0);
        }
        
        .timeline-item:nth-child(odd) .timeline-content {
            margin-left: 0;
            margin-right: calc(50% + 3rem);
            text-align: right;
        }
        
        .timeline-item:nth-child(even) .timeline-content {
            margin-left: calc(50% + 3rem);
            margin-right: 0;
            text-align: left;
        }
        
        .timeline-marker {
            position: absolute;
            left: 50%;
            top: 2rem;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #dc2626, #fbbf24);
            border-radius: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 0 8px rgba(220, 38, 38, 0.2);
            z-index: 2;
        }
        
        .timeline-marker i {
            color: white;
            font-size: 1.5rem;
        }
        
        .timeline-content {
            background: white;
            padding: 2.5rem;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            position: relative;
            transition: transform 0.3s ease;
        }
        
        .timeline-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .timeline-item:nth-child(odd) .timeline-content::after {
            content: '';
            position: absolute;
            right: -15px;
            top: 2rem;
            width: 0;
            height: 0;
            border-left: 15px solid white;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
        }
        
        .timeline-item:nth-child(even) .timeline-content::after {
            content: '';
            position: absolute;
            left: -15px;
            top: 2rem;
            width: 0;
            height: 0;
            border-right: 15px solid white;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
        }
        
        .timeline-year {
            display: inline-block;
            background: linear-gradient(135deg, #dc2626, #991b1b);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        
        .timeline-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
            line-height: 1.3;
        }
        
        .timeline-description {
            color: #6b7280;
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .timeline-details {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid #dc2626;
        }
        
        .timeline-details h4 {
            color: #dc2626;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .timeline-details ul {
            list-style: none;
            padding-left: 0;
        }
        
        .timeline-details li {
            padding: 0.3rem 0;
            position: relative;
            padding-left: 1.5rem;
        }
        
        .timeline-details li::before {
            content: '•';
            color: #dc2626;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .back-button {
            position: fixed;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.9);
            color: #1f2937;
            text-decoration: none;
            padding: 1rem 1.5rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }
        
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        @media (max-width: 768px) {
            .timeline-line {
                left: 2rem;
            }
            
            .timeline-marker {
                left: 2rem;
                width: 40px;
                height: 40px;
            }
            
            .timeline-marker i {
                font-size: 1rem;
            }
            
            .timeline-item:nth-child(odd) .timeline-content,
            .timeline-item:nth-child(even) .timeline-content {
                margin-left: 5rem;
                margin-right: 0;
                text-align: left;
            }
            
            .timeline-item:nth-child(odd) .timeline-content::after,
            .timeline-item:nth-child(even) .timeline-content::after {
                left: -15px;
                right: auto;
                border-right: 15px solid white;
                border-left: none;
            }
            
            .timeline-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="timeline-page">
        <a href="index.html" class="back-button">
            <i class="fas fa-arrow-left"></i> Về trang chủ
        </a>
        
        <div class="timeline-container">
            <div class="timeline-header">
                <h1><i class="fas fa-history"></i> Timeline CNXH Việt Nam</h1>
                <p>Hành trình lịch sử phát triển Chủ nghĩa Xã hội tại Việt Nam</p>
            </div>
            
            <div class="timeline-wrapper">
                <div class="timeline-line"></div>
                
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <i class="fas fa-flag"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-year">1930</div>
                        <h3 class="timeline-title">Thành lập Đảng Cộng sản Việt Nam</h3>
                        <p class="timeline-description">
                            Chủ tịch Hồ Chí Minh thành lập Đảng Cộng sản Việt Nam, xác định con đường cách mạng vô sản cho dân tộc Việt Nam.
                        </p>
                        <div class="timeline-details">
                            <h4>Ý nghĩa lịch sử:</h4>
                            <ul>
                                <li>Đánh dấu sự ra đời của lực lượng lãnh đạo cách mạng</li>
                                <li>Xác định con đường giải phóng dân tộc và xã hội</li>
                                <li>Kết hợp chủ nghĩa yêu nước với chủ nghĩa xã hội</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-year">1945</div>
                        <h3 class="timeline-title">Cách mạng Tháng Tám thành công</h3>
                        <p class="timeline-description">
                            Giành độc lập, thành lập nước Việt Nam Dân chủ Cộng hòa - nhà nước công nông đầu tiên ở Đông Nam Á.
                        </p>
                        <div class="timeline-details">
                            <h4>Thành tựu quan trọng:</h4>
                            <ul>
                                <li>Tuyên bố độc lập ngày 2/9/1945</li>
                                <li>Thành lập chính quyền nhân dân</li>
                                <li>Bắt đầu quá trình xây dựng nhà nước mới</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <i class="fas fa-road"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-year">1960</div>
                        <h3 class="timeline-title">Bước vào thời kỳ quá độ</h3>
                        <p class="timeline-description">
                            Sau 9 năm kháng chiến chống Pháp và thời gian cải tạo xã hội, Việt Nam chính thức bước vào thời kỳ quá độ lên chủ nghĩa xã hội.
                        </p>
                        <div class="timeline-details">
                            <h4>Đặc điểm thời kỳ:</h4>
                            <ul>
                                <li>Bỏ qua chế độ tư bản chủ nghĩa</li>
                                <li>Cải tạo quan hệ sản xuất</li>
                                <li>Xây dựng nền kinh tế xã hội chủ nghĩa</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-year">1986</div>
                        <h3 class="timeline-title">Đổi mới toàn diện</h3>
                        <p class="timeline-description">
                            Đại hội VI của Đảng khởi xướng công cuộc Đổi mới toàn diện, chuyển sang nền kinh tế thị trường định hướng xã hội chủ nghĩa.
                        </p>
                        <div class="timeline-details">
                            <h4>Nội dung đổi mới:</h4>
                            <ul>
                                <li>Đổi mới tư duy kinh tế</li>
                                <li>Phát triển kinh tế nhiều thành phần</li>
                                <li>Mở cửa hội nhập quốc tế</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-year">2011</div>
                        <h3 class="timeline-title">Cương lĩnh bổ sung, phát triển</h3>
                        <p class="timeline-description">
                            Đại hội XI bổ sung, phát triển Cương lĩnh xây dựng đất nước trong thời kỳ quá độ lên CNXH với 8 đặc trưng cơ bản.
                        </p>
                        <div class="timeline-details">
                            <h4>Những điểm mới:</h4>
                            <ul>
                                <li>Xác định rõ 8 đặc trưng cơ bản của CNXH Việt Nam</li>
                                <li>8 phương hướng xây dựng đất nước</li>
                                <li>Hoàn thiện lý luận về CNXH Việt Nam</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-marker">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="timeline-year">2024</div>
                        <h3 class="timeline-title">Phát triển hiện tại</h3>
                        <p class="timeline-description">
                            Việt Nam tiếp tục phát triển mạnh mẽ với GDP tăng trưởng 8.2%, hội nhập sâu rộng và hiện đại hóa toàn diện.
                        </p>
                        <div class="timeline-details">
                            <h4>Thành tựu nổi bật:</h4>
                            <ul>
                                <li>Nền kinh tế trong top 20 thế giới</li>
                                <li>Quan hệ đối tác chiến lược với nhiều nước</li>
                                <li>Chuyển đổi số và công nghiệp 4.0</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Animate timeline items on scroll
        function animateOnScroll() {
            const items = document.querySelectorAll('.timeline-item');
            
            items.forEach(item => {
                const itemTop = item.getBoundingClientRect().top;
                const itemBottom = item.getBoundingClientRect().bottom;
                
                if (itemTop < window.innerHeight && itemBottom > 0) {
                    item.classList.add('animate');
                }
            });
        }
        
        // Initial animation
        function initialAnimation() {
            const items = document.querySelectorAll('.timeline-item');
            
            items.forEach((item, index) => {
                setTimeout(() => {
                    item.classList.add('animate');
                }, index * 300);
            });
        }
        
        // Smooth scroll for better UX
        function smoothScroll() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            // Start initial animation after a short delay
            setTimeout(initialAnimation, 500);
            
            // Add scroll listener for additional animations
            window.addEventListener('scroll', animateOnScroll);
        });
        
        // Add hover effects
        document.addEventListener('DOMContentLoaded', () => {
            const timelineContents = document.querySelectorAll('.timeline-content');
            
            timelineContents.forEach(content => {
                content.addEventListener('mouseenter', () => {
                    content.style.transform = 'translateY(-10px) scale(1.02)';
                    content.style.boxShadow = '0 25px 50px rgba(0, 0, 0, 0.4)';
                });
                
                content.addEventListener('mouseleave', () => {
                    content.style.transform = 'translateY(0) scale(1)';
                    content.style.boxShadow = '0 15px 35px rgba(0, 0, 0, 0.2)';
                });
            });
        });
    </script>
</body>
</html>
