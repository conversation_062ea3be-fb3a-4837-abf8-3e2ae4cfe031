/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header and Navigation */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc2626;
}

.nav-logo i {
    margin-right: 0.5rem;
    color: #fbbf24;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #dc2626;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 0;
    background-color: #dc2626;
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    display: flex;
    align-items: center;
    min-height: 100vh;
    padding: 120px 20px 80px;
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
    color: white;
}

.hero-content {
    flex: 1;
    max-width: 600px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    padding: 1rem 2rem;
    background: #fbbf24;
    color: #333;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
}

.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image i {
    font-size: 15rem;
    opacity: 0.3;
}

/* Section Styles */
.section {
    padding: 80px 0;
}

.bg-light {
    background-color: #f8fafc;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: #1f2937;
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Content Cards */
.content-card {
    display: flex;
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.card-icon {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
}

.card-icon i {
    font-size: 2rem;
    color: white;
}

.card-content {
    flex: 1;
}

.highlight-box {
    background: #fef3c7;
    border-left: 4px solid #fbbf24;
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 10px 10px 0;
}

.highlight-box h4 {
    color: #92400e;
    margin-bottom: 1rem;
    font-weight: 600;
}

.highlight-box ul {
    list-style: none;
    padding-left: 0;
}

.highlight-box li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.highlight-box li::before {
    content: '•';
    color: #dc2626;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.note {
    font-style: italic;
    color: #6b7280;
    background: #f1f5f9;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
}

/* Conditions Grid */
.conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.condition-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.condition-card:hover {
    transform: translateY(-5px);
}

.card-header {
    background: linear-gradient(135deg, #dc2626, #991b1b);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.card-header i {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.card-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 2rem;
}

.card-body h4 {
    color: #dc2626;
    margin: 1.5rem 0 1rem 0;
    font-weight: 600;
}

.card-body ul {
    list-style: none;
    padding-left: 0;
}

.card-body li {
    padding: 0.75rem 0;
    position: relative;
    padding-left: 2rem;
    border-bottom: 1px solid #f1f5f9;
}

.card-body li:last-child {
    border-bottom: none;
}

.card-body li::before {
    content: '→';
    color: #dc2626;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Timeline */
.timeline {
    margin-top: 2rem;
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
}

.year {
    background: #dc2626;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    margin-right: 1rem;
    min-width: 100px;
    text-align: center;
}

.event {
    flex: 1;
    font-weight: 500;
}

/* World Impact */
.world-impact {
    margin-top: 3rem;
}

.world-impact h3 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #1f2937;
}

.impact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.impact-item {
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.impact-item:hover {
    transform: translateY(-5px);
}

.impact-item.positive {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.impact-item.negative {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.impact-item i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.impact-item h4 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

/* Transition Content */
.transition-content {
    margin: 3rem 0;
}

.content-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.content-box {
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.content-box.skip {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.content-box.inherit {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.content-box h3 {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
}

.content-box h3 i {
    margin-right: 0.5rem;
    font-size: 1.5rem;
}

.content-box ul {
    list-style: none;
    padding-left: 0;
}

.content-box li {
    padding: 0.75rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.content-box li::before {
    content: '•';
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

/* Conclusion */
.conclusion {
    margin-top: 4rem;
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.conclusion h3 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #1f2937;
}

.conclusion-content p {
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.key-points {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.point {
    display: flex;
    align-items: flex-start;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 10px;
    border-left: 4px solid #dc2626;
}

.point i {
    color: #dc2626;
    margin-right: 1rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.point span {
    flex: 1;
    font-weight: 500;
}

/* Characteristics Grid */
.characteristics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.characteristic-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.characteristic-card:hover {
    transform: translateY(-5px);
}

.characteristic-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #dc2626, #fbbf24);
}

.card-number {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
}

.characteristic-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
    line-height: 1.3;
}

.characteristic-card p {
    color: #6b7280;
    line-height: 1.6;
}

/* Directions Container */
.directions-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 3rem;
}

.direction-item {
    display: flex;
    align-items: flex-start;
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.direction-item:hover {
    transform: translateX(10px);
}

.direction-icon {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2rem;
}

.direction-icon i {
    font-size: 2rem;
    color: white;
}

.direction-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1f2937;
}

.direction-content p {
    color: #6b7280;
    line-height: 1.6;
    font-size: 1.1rem;
}

/* Hero Stats */
.hero-stats {
    display: flex;
    gap: 2rem;
    margin: 2rem 0;
    justify-content: center;
}

.stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #fbbf24;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Interactive Features */
.interactive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.interactive-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.interactive-card:hover {
    transform: translateY(-5px);
}

.interactive-card .card-header {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
}

.interactive-card .card-header i {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.interactive-card .card-body {
    padding: 2rem;
    text-align: center;
}

.interactive-btn {
    background: linear-gradient(135deg, #dc2626, #991b1b);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.interactive-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 38, 38, 0.3);
}

/* Chatbot Popup Styles */
.chatbot-popup {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 2000;
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.chatbot-header {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chatbot-header h4 {
    margin: 0;
    font-size: 1.1rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chatbot-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 1rem;
}

/* Remove unused quiz and timeline styles since they're now on separate pages */

.chat-messages {
    flex: 1;
    overflow-y: auto;
    background: #f8fafc;
    border-radius: 10px;
    margin-bottom: 1rem;
    padding: 0.5rem;
    max-height: 300px;
}

.bot-message {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.bot-message i {
    color: #10b981;
    margin-right: 0.5rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.bot-message p {
    background: white;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.user-message {
    text-align: right;
    margin-bottom: 0.75rem;
}

.user-message p {
    background: #dc2626;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
    display: inline-block;
    max-width: 80%;
    margin: 0;
    font-size: 0.9rem;
}

.chat-input-container {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.chat-input-container input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 20px;
    outline: none;
    font-size: 0.9rem;
}

.chat-input-container input:focus {
    border-color: #10b981;
}

.chat-input-container button {
    background: #10b981;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.chat-input-container button:hover {
    background: #059669;
}

.quick-questions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.quick-questions p {
    width: 100%;
    margin: 0 0 0.5rem 0;
    font-size: 0.8rem;
    color: #6b7280;
}

.quick-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    flex: 1;
    min-width: 80px;
}

.quick-btn:hover {
    background: #10b981;
    color: white;
    border-color: #10b981;
}

@media (max-width: 768px) {
    .chatbot-popup {
        width: calc(100vw - 2rem);
        right: 1rem;
        bottom: 1rem;
        height: 400px;
    }
}

/* Context Grid */
.context-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.context-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.context-card:hover {
    transform: translateY(-5px);
}

.context-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dc2626, #991b1b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.context-icon i {
    font-size: 2rem;
    color: white;
}

.context-stats {
    display: flex;
    gap: 2rem;
    margin: 1.5rem 0;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #dc2626;
}

.stat-desc {
    font-size: 0.9rem;
    color: #6b7280;
}

/* Achievements */
.achievements-section {
    margin-top: 4rem;
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.achievements-section h3 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #1f2937;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.achievement-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 15px;
    border-left: 4px solid #dc2626;
}

.achievement-year {
    background: #dc2626;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    font-weight: 700;
    margin-right: 1.5rem;
    min-width: 60px;
    text-align: center;
}

.achievement-content h4 {
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.achievement-content p {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fbbf24;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #e5e7eb;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #fbbf24;
}

/* AI Usage Styles */
.ai-usage-container {
    max-width: 1000px;
    margin: 0 auto;
}

.ai-section {
    background: white;
    margin-bottom: 2rem;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.ai-section h3 {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    color: #1f2937;
    font-size: 1.5rem;
}

.ai-section h3 i {
    margin-right: 0.5rem;
    color: #dc2626;
}

.ai-tools, .prompt-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.tool-item, .prompt-item {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #10b981;
}

.tool-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.tool-purpose {
    color: #6b7280;
}

.work-division {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.ai-work, .student-work {
    padding: 1.5rem;
    border-radius: 15px;
}

.ai-work {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
    border-left: 4px solid #f59e0b;
}

.student-work {
    background: linear-gradient(135deg, #dbeafe, #3b82f6);
    color: white;
    border-left: 4px solid #2563eb;
}

.ai-work h4, .student-work h4 {
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.ai-work ul, .student-work ul {
    list-style: none;
    padding-left: 0;
}

.ai-work li, .student-work li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.ai-work li::before {
    content: '🤖';
    position: absolute;
    left: 0;
}

.student-work li::before {
    content: '👨‍🎓';
    position: absolute;
    left: 0;
}

.source-verification {
    background: #f0fdf4;
    padding: 2rem;
    border-radius: 15px;
    border-left: 4px solid #10b981;
}

.source-list {
    list-style: none;
    padding-left: 0;
    margin-top: 1rem;
}

.source-list li {
    padding: 0.75rem 0;
    display: flex;
    align-items: center;
}

.source-list li i {
    color: #10b981;
    margin-right: 1rem;
    width: 20px;
}

.commitment-section {
    background: linear-gradient(135deg, #fee2e2, #fecaca) !important;
    border: 2px solid #dc2626;
}

.commitment-box {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    border-left: 4px solid #dc2626;
}

.commitment-text ul {
    list-style: none;
    padding-left: 0;
    margin: 1rem 0;
}

.commitment-text li {
    padding: 0.5rem 0;
    font-weight: 500;
}

.signature {
    margin-top: 2rem;
    text-align: right;
    font-style: italic;
    color: #6b7280;
}

.creative-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.feature-item i {
    font-size: 2rem;
    color: #dc2626;
    margin-right: 1rem;
    flex-shrink: 0;
}

.feature-item h4 {
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.feature-item p {
    color: #6b7280;
    font-size: 0.9rem;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1rem;
    text-align: center;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 20px 60px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-image {
        margin-top: 2rem;
    }
    
    .hero-image i {
        font-size: 8rem;
    }
    
    .conditions-grid {
        grid-template-columns: 1fr;
    }
    
    .content-row {
        grid-template-columns: 1fr;
    }
    
    .characteristics-grid {
        grid-template-columns: 1fr;
    }
    
    .direction-item {
        flex-direction: column;
        text-align: center;
    }
    
    .direction-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .impact-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .content-card {
        flex-direction: column;
        text-align: center;
    }
    
    .card-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
}
