// Mobile Navigation Toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    hamburger.classList.remove('active');
    navMenu.classList.remove('active');
}));

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const headerOffset = 80;
            const elementPosition = target.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    });
});

// Header background change on scroll
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(255, 255, 255, 0.98)';
        header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.15)';
    } else {
        header.style.background = 'rgba(255, 255, 255, 0.95)';
        header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    }
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', () => {
    // Add animation classes to elements
    const animatedElements = document.querySelectorAll(
        '.content-card, .condition-card, .characteristic-card, .direction-item, .impact-item'
    );
    
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Add active class to navigation links based on scroll position
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.getBoundingClientRect().top;
        const sectionHeight = section.clientHeight;
        if (sectionTop <= 100 && sectionTop + sectionHeight > 100) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Add hover effects for cards
document.addEventListener('DOMContentLoaded', () => {
    const cards = document.querySelectorAll('.characteristic-card, .direction-item');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
            card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
            card.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
        });
    });
});

// Parallax effect for hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    const heroImage = document.querySelector('.hero-image i');
    
    if (hero && heroImage) {
        const rate = scrolled * -0.5;
        heroImage.style.transform = `translateY(${rate}px)`;
    }
});

// Add typing effect to hero title
document.addEventListener('DOMContentLoaded', () => {
    const heroTitle = document.querySelector('.hero-title');
    if (heroTitle) {
        const text = heroTitle.textContent;
        heroTitle.textContent = '';
        heroTitle.style.borderRight = '2px solid #fbbf24';
        
        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                heroTitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 100);
            } else {
                setTimeout(() => {
                    heroTitle.style.borderRight = 'none';
                }, 1000);
            }
        };
        
        setTimeout(typeWriter, 1000);
    }
});

// Add counter animation for numbered items
const animateCounters = () => {
    const counters = document.querySelectorAll('.card-number');
    
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        let current = 0;
        const increment = target / 50;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        // Start animation when element is visible
        const counterObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    updateCounter();
                    counterObserver.unobserve(entry.target);
                }
            });
        });
        
        counterObserver.observe(counter);
    });
};

// Initialize counter animation
document.addEventListener('DOMContentLoaded', animateCounters);

// Add search functionality (basic)
const addSearchFunctionality = () => {
    // Create search input
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <input type="text" id="searchInput" placeholder="Tìm kiếm nội dung..." class="search-input">
        <i class="fas fa-search search-icon"></i>
    `;
    
    // Add search styles
    const searchStyles = `
        .search-container {
            position: relative;
            max-width: 400px;
            margin: 2rem auto;
        }
        .search-input {
            width: 100%;
            padding: 1rem 3rem 1rem 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 50px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        .search-input:focus {
            outline: none;
            border-color: #dc2626;
        }
        .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = searchStyles;
    document.head.appendChild(styleSheet);
    
    // Insert search container after hero section
    const hero = document.querySelector('.hero');
    if (hero && hero.nextElementSibling) {
        hero.nextElementSibling.insertBefore(searchContainer, hero.nextElementSibling.firstChild);
    }
    
    // Add search functionality
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            const searchTerm = e.target.value.toLowerCase();
            const sections = document.querySelectorAll('section[id]');
            
            sections.forEach(section => {
                const text = section.textContent.toLowerCase();
                if (searchTerm && !text.includes(searchTerm)) {
                    section.style.opacity = '0.3';
                } else {
                    section.style.opacity = '1';
                }
            });
            
            if (!searchTerm) {
                sections.forEach(section => {
                    section.style.opacity = '1';
                });
            }
        });
    }
};

// Initialize search functionality
document.addEventListener('DOMContentLoaded', addSearchFunctionality);

// Add print functionality
const addPrintButton = () => {
    const printButton = document.createElement('button');
    printButton.innerHTML = '<i class="fas fa-print"></i> In trang';
    printButton.className = 'print-button';
    printButton.style.cssText = `
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        background: #dc2626;
        color: white;
        border: none;
        padding: 1rem;
        border-radius: 50px;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        transition: all 0.3s ease;
        z-index: 1000;
        font-weight: 600;
    `;
    
    printButton.addEventListener('mouseenter', () => {
        printButton.style.transform = 'translateY(-2px)';
        printButton.style.boxShadow = '0 8px 25px rgba(220, 38, 38, 0.4)';
    });
    
    printButton.addEventListener('mouseleave', () => {
        printButton.style.transform = 'translateY(0)';
        printButton.style.boxShadow = '0 4px 15px rgba(220, 38, 38, 0.3)';
    });
    
    printButton.addEventListener('click', () => {
        window.print();
    });
    
    document.body.appendChild(printButton);
};

// Initialize print button
document.addEventListener('DOMContentLoaded', addPrintButton);

// Quiz functionality
const quizQuestions = [
    {
        question: "Việt Nam có bao nhiêu đặc trưng cơ bản của CNXH?",
        options: ["6 đặc trưng", "8 đặc trưng", "10 đặc trưng", "12 đặc trưng"],
        correct: 1,
        explanation: "Việt Nam có 8 đặc trưng cơ bản của CNXH theo Cương lĩnh 2011."
    },
    {
        question: "Tại sao Việt Nam bỏ qua chế độ tư bản chủ nghĩa?",
        options: [
            "Do điều kiện kinh tế không cho phép",
            "Theo con đường cách mạng vô sản của Hồ Chí Minh",
            "Do áp lực từ các nước xã hội chủ nghĩa",
            "Do không có tư bản tư nhân"
        ],
        correct: 1,
        explanation: "Chủ tịch Hồ Chí Minh đã xác định con đường cách mạng vô sản, đi thẳng lên CNXH."
    },
    {
        question: "Năm nào Việt Nam chính thức bước vào thời kỳ quá độ?",
        options: ["1945", "1954", "1960", "1975"],
        correct: 2,
        explanation: "Năm 1960, Việt Nam chính thức bước vào thời kỳ quá độ lên CNXH."
    }
];

let currentQuestionIndex = 0;
let userScore = 0;

function startQuiz() {
    document.getElementById('quizModal').style.display = 'block';
    currentQuestionIndex = 0;
    userScore = 0;
    showQuestion();
}

function closeQuiz() {
    document.getElementById('quizModal').style.display = 'none';
}

function showQuestion() {
    const question = quizQuestions[currentQuestionIndex];
    document.getElementById('questionText').textContent = question.question;

    const options = document.querySelectorAll('.quiz-option');
    options.forEach((option, index) => {
        option.textContent = `${String.fromCharCode(65 + index)}. ${question.options[index]}`;
        option.className = 'quiz-option';
        option.onclick = () => selectAnswer(index);
    });

    document.getElementById('quizResult').style.display = 'none';
    document.querySelector('.quiz-question').style.display = 'block';
}

function selectAnswer(selectedIndex) {
    const question = quizQuestions[currentQuestionIndex];
    const options = document.querySelectorAll('.quiz-option');

    options.forEach((option, index) => {
        if (index === question.correct) {
            option.classList.add('correct');
        } else if (index === selectedIndex && index !== question.correct) {
            option.classList.add('incorrect');
        }
        option.onclick = null;
    });

    if (selectedIndex === question.correct) {
        userScore++;
    }

    document.getElementById('resultText').innerHTML = `
        <strong>${selectedIndex === question.correct ? 'Chính xác!' : 'Sai rồi!'}</strong><br>
        ${question.explanation}
    `;

    document.querySelector('.quiz-question').style.display = 'none';
    document.getElementById('quizResult').style.display = 'block';
}

function nextQuestion() {
    currentQuestionIndex++;
    if (currentQuestionIndex < quizQuestions.length) {
        showQuestion();
    } else {
        showQuizResult();
    }
}

function showQuizResult() {
    const percentage = Math.round((userScore / quizQuestions.length) * 100);
    document.getElementById('quizResult').innerHTML = `
        <h4>Kết quả Quiz</h4>
        <p>Bạn đã trả lời đúng ${userScore}/${quizQuestions.length} câu (${percentage}%)</p>
        <p>${percentage >= 70 ? 'Xuất sắc! Bạn đã nắm vững kiến thức.' : 'Hãy ôn tập thêm để hiểu rõ hơn về CNXH Việt Nam.'}</p>
        <button onclick="startQuiz()">Làm lại</button>
        <button onclick="closeQuiz()">Đóng</button>
    `;
}

// Timeline functionality
function showTimeline() {
    document.getElementById('timelineModal').style.display = 'block';
    animateTimeline();
}

function closeTimeline() {
    document.getElementById('timelineModal').style.display = 'none';
}

function animateTimeline() {
    const events = document.querySelectorAll('.timeline-event');
    events.forEach((event, index) => {
        setTimeout(() => {
            event.style.opacity = '0';
            event.style.transform = 'translateY(30px)';
            event.style.transition = 'all 0.6s ease';

            setTimeout(() => {
                event.style.opacity = '1';
                event.style.transform = 'translateY(0)';
            }, 100);
        }, index * 200);
    });
}

// Chatbot functionality
const chatbotResponses = {
    "8 đặc trưng cơ bản của CNXH Việt Nam là gì?": `8 đặc trưng cơ bản của CNXH Việt Nam:
1. Dân giàu, nước mạnh, dân chủ, công bằng, văn minh
2. Do nhân dân làm chủ
3. Có nền kinh tế phát triển cao
4. Có nền văn hóa tiên tiến, đậm đà bản sắc dân tộc
5. Con người có cuộc sống ấm no, tự do, hạnh phúc
6. Các dân tộc bình đẳng, đoàn kết
7. Có Nhà nước pháp quyền xã hội chủ nghĩa
8. Có quan hệ hữu nghị và hợp tác với các nước`,

    "Tại sao Việt Nam bỏ qua chế độ tư bản chủ nghĩa?": `Việt Nam bỏ qua chế độ tư bản chủ nghĩa vì:
- Chủ tịch Hồ Chí Minh đã xác định con đường cách mạng vô sản
- Phù hợp với xu thế vận động thời bấy giờ
- Đi thẳng lên chế độ xã hội chủ nghĩa sau cách mạng
- Không xác lập sự thống trị của phương thức sản xuất tư bản chủ nghĩa`,

    "Điều kiện chủ quan của Việt Nam như thế nào?": `Điều kiện chủ quan của Việt Nam:
- Xuất phát từ nước nửa thuộc địa, nửa phong kiến lạc hậu
- Chịu hậu quả nặng nề của chiến tranh
- Tài nguyên cạn kiệt do thực dân vơ vét
- Nguồn nhân lực yếu, xã hội thuần nông
- Tàn dư xã hội cũ nặng nề
- Hạn chế về trình độ và kinh nghiệm lãnh đạo`
};

function openChatbot() {
    document.getElementById('chatbotModal').style.display = 'block';
}

function closeChatbot() {
    document.getElementById('chatbotModal').style.display = 'none';
}

function sendMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();

    if (message) {
        addUserMessage(message);
        input.value = '';

        setTimeout(() => {
            const response = getBotResponse(message);
            addBotMessage(response);
        }, 1000);
    }
}

function addUserMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'user-message';
    messageDiv.innerHTML = `<p>${message}</p>`;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function addBotMessage(message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'bot-message';
    messageDiv.innerHTML = `<i class="fas fa-robot"></i><p>${message}</p>`;
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function getBotResponse(message) {
    // Simple keyword matching
    for (const [key, response] of Object.entries(chatbotResponses)) {
        if (message.toLowerCase().includes(key.toLowerCase()) ||
            key.toLowerCase().includes(message.toLowerCase())) {
            return response;
        }
    }

    // Default responses based on keywords
    if (message.toLowerCase().includes('đặc trưng')) {
        return "CNXH Việt Nam có 8 đặc trưng cơ bản được xác định trong Cương lĩnh 2011. Bạn muốn tôi liệt kê chi tiết không?";
    } else if (message.toLowerCase().includes('phương hướng')) {
        return "Có 8 phương hướng xây dựng CNXH ở Việt Nam, bao gồm công nghiệp hóa, kinh tế thị trường định hướng XHCN, văn hóa tiên tiến, v.v.";
    } else if (message.toLowerCase().includes('quá độ')) {
        return "Thời kỳ quá độ lên CNXH của Việt Nam bắt đầu từ 1960, với đặc điểm bỏ qua chế độ tư bản chủ nghĩa.";
    } else {
        return "Tôi hiểu bạn đang quan tâm về CNXH Việt Nam. Bạn có thể hỏi cụ thể về 8 đặc trưng, 8 phương hướng, hoặc quá trình quá độ.";
    }
}

function askQuestion(question) {
    document.getElementById('chatInput').value = question;
    sendMessage();
}

// Handle Enter key in chat input
document.addEventListener('DOMContentLoaded', () => {
    const chatInput = document.getElementById('chatInput');
    if (chatInput) {
        chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }
});

// Close modals when clicking outside
window.addEventListener('click', (e) => {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });
});

// Animate hero stats
document.addEventListener('DOMContentLoaded', () => {
    const statNumbers = document.querySelectorAll('.stat-number');

    const animateStats = () => {
        statNumbers.forEach(stat => {
            const target = parseInt(stat.getAttribute('data-target'));
            let current = 0;
            const increment = target / 50;

            const updateStat = () => {
                if (current < target) {
                    current += increment;
                    stat.textContent = Math.ceil(current);
                    requestAnimationFrame(updateStat);
                } else {
                    stat.textContent = target;
                }
            };

            updateStat();
        });
    };

    // Start animation after page load
    setTimeout(animateStats, 2000);
});
